import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  BarChart3, 
  Users, 
  Package, 
  DollarSign, 
  Settings, 
  LogOut, 
  Menu, 
  X,
  TrendingUp,
  TrendingDown,
  ShoppingCart,
  Eye,
  Star,
  Calendar,
  Clock,
  ArrowUpRight,
  ArrowDownRight,
  Activity,
  CreditCard,
  Truck,
  CheckCircle,
  AlertCircle,
  XCircle
} from 'lucide-react';
import AdminLayout from '../components/AdminLayout';

const AdminDashboardPage: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is authenticated
    const isAuthenticated = localStorage.getItem('adminAuthenticated');
    if (!isAuthenticated) {
      navigate('/admin/login');
    } else {
      // Simulate loading
      setTimeout(() => setIsLoading(false), 1000);
    }
  }, [navigate]);

  const handleLogout = () => {
    localStorage.removeItem('adminAuthenticated');
    navigate('/admin/login');
  };

  const stats = [
    {
      title: 'Total Revenue',
      value: '$124,567',
      change: '+12.5%',
      changeType: 'positive',
      icon: DollarSign,
      color: 'bg-green-500',
      detail: 'vs last month'
    },
    {
      title: 'Total Orders',
      value: '2,847',
      change: '+8.2%',
      changeType: 'positive',
      icon: ShoppingCart,
      color: 'bg-blue-500',
      detail: 'vs last month'
    },
    {
      title: 'Total Customers',
      value: '1,234',
      change: '+15.3%',
      changeType: 'positive',
      icon: Users,
      color: 'bg-purple-500',
      detail: 'vs last month'
    },
    {
      title: 'Total Products',
      value: '156',
      change: '-2.1%',
      changeType: 'negative',
      icon: Package,
      color: 'bg-orange-500',
      detail: 'vs last month'
    }
  ];

  const recentOrders = [
    { 
      id: '#1234', 
      customer: 'John Doe', 
      email: '<EMAIL>',
      amount: '$299.99', 
      status: 'Completed', 
      date: '2024-01-15',
      items: 3
    },
    { 
      id: '#1235', 
      customer: 'Jane Smith', 
      email: '<EMAIL>',
      amount: '$199.99', 
      status: 'Pending', 
      date: '2024-01-14',
      items: 2
    },
    { 
      id: '#1236', 
      customer: 'Bob Johnson', 
      email: '<EMAIL>',
      amount: '$399.99', 
      status: 'Processing', 
      date: '2024-01-13',
      items: 1
    },
    { 
      id: '#1237', 
      customer: 'Alice Brown', 
      email: '<EMAIL>',
      amount: '$149.99', 
      status: 'Completed', 
      date: '2024-01-12',
      items: 4
    },
    { 
      id: '#1238', 
      customer: 'Charlie Wilson', 
      email: '<EMAIL>',
      amount: '$89.99', 
      status: 'Shipped', 
      date: '2024-01-11',
      items: 2
    }
  ];

  const recentActivities = [
    {
      id: 1,
      type: 'order',
      message: 'New order #1238 received from Charlie Wilson',
      time: '2 minutes ago',
      icon: ShoppingCart,
      color: 'text-blue-500'
    },
    {
      id: 2,
      type: 'customer',
      message: 'New customer registration: Sarah Miller',
      time: '15 minutes ago',
      icon: Users,
      color: 'text-green-500'
    },
    {
      id: 3,
      type: 'product',
      message: 'Product "Wireless Headphones" stock updated',
      time: '1 hour ago',
      icon: Package,
      color: 'text-orange-500'
    },
    {
      id: 4,
      type: 'payment',
      message: 'Payment received for order #1234',
      time: '2 hours ago',
      icon: CreditCard,
      color: 'text-purple-500'
    }
  ];

  const topProducts = [
    {
      name: 'Wireless Headphones',
      sales: 234,
      revenue: '$23,400',
      growth: '+12%',
      image: '🎧'
    },
    {
      name: 'Smart Watch',
      sales: 189,
      revenue: '$18,900',
      growth: '+8%',
      image: '⌚'
    },
    {
      name: 'Laptop Stand',
      sales: 156,
      revenue: '$7,800',
      growth: '+15%',
      image: '💻'
    },
    {
      name: 'Bluetooth Speaker',
      sales: 123,
      revenue: '$12,300',
      growth: '+5%',
      image: '🔊'
    }
  ];

  const navigation = [
    { name: 'Dashboard', icon: BarChart3, href: '/admin' },
    { name: 'Products', icon: Package, href: '/admin/products' },
    { name: 'Orders', icon: ShoppingCart, href: '/admin/orders' },
    { name: 'Customers', icon: Users, href: '/admin/customers' },
    { name: 'Settings', icon: Settings, href: '/admin/settings' },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-100 text-green-800';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'Processing':
        return 'bg-blue-100 text-blue-800';
      case 'Shipped':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const renderDashboard = () => (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg shadow-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Welcome back, Admin!</h1>
            <p className="text-blue-100 mt-1">Here's what's happening with your store today.</p>
          </div>
          <div className="text-right">
            <p className="text-blue-100 text-sm">Today's Date</p>
            <p className="text-xl font-semibold">{new Date().toLocaleDateString()}</p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className={`p-3 rounded-full ${stat.color} text-white`}>
                  <stat.icon className="h-6 w-6" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                </div>
              </div>
              <div className="text-right">
                <div className={`flex items-center ${stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'}`}>
                  {stat.changeType === 'positive' ? (
                    <ArrowUpRight className="h-4 w-4 mr-1" />
                  ) : (
                    <ArrowDownRight className="h-4 w-4 mr-1" />
                  )}
                  <span className="text-sm font-medium">{stat.change}</span>
                </div>
                <p className="text-xs text-gray-500 mt-1">{stat.detail}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts and Analytics Row */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Sales Chart */}
        <div className="lg:col-span-2 bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Sales Overview</h3>
            <div className="flex space-x-2">
              <button className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md">7 Days</button>
              <button className="px-3 py-1 text-sm text-gray-500 hover:bg-gray-100 rounded-md">30 Days</button>
              <button className="px-3 py-1 text-sm text-gray-500 hover:bg-gray-100 rounded-md">90 Days</button>
            </div>
          </div>
          <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">Sales chart will be implemented here</p>
              <p className="text-sm text-gray-400">Integration with chart library</p>
            </div>
          </div>
        </div>

        {/* Recent Activities */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activities</h3>
          <div className="space-y-4">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className={`p-2 rounded-full bg-gray-100 ${activity.color}`}>
                  <activity.icon className="h-4 w-4" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-900">{activity.message}</p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Orders and Top Products */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Orders */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Recent Orders</h3>
            <button className="text-sm text-blue-600 hover:text-blue-800">View all</button>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Order
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {recentOrders.slice(0, 4).map((order) => (
                  <tr key={order.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{order.id}</div>
                        <div className="text-sm text-gray-500">{order.items} items</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{order.customer}</div>
                        <div className="text-sm text-gray-500">{order.email}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {order.amount}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                        {order.status}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Top Products</h3>
            <button className="text-sm text-blue-600 hover:text-blue-800">View all</button>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {topProducts.map((product, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center text-lg">
                      {product.image}
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">{product.name}</p>
                    <p className="text-sm text-gray-500">{product.sales} sales</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">{product.revenue}</p>
                    <p className="text-xs text-green-600">{product.growth}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return renderDashboard();
      case 'products':
        return (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Products Management</h3>
            <p className="text-gray-600">Product management interface will be implemented here.</p>
          </div>
        );
      case 'orders':
        return (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Orders Management</h3>
            <p className="text-gray-600">Order management interface will be implemented here.</p>
          </div>
        );
      case 'customers':
        return (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Customer Management</h3>
            <p className="text-gray-600">Customer management interface will be implemented here.</p>
          </div>
        );
      case 'settings':
        return (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Settings</h3>
            <p className="text-gray-600">Settings interface will be implemented here.</p>
          </div>
        );
      default:
        return renderDashboard();
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="min-h-screen bg-gray-100 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading dashboard...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      {renderContent()}
    </AdminLayout>
  );
};

export default AdminDashboardPage; 